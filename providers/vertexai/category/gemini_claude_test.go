package category

import (
	"done-hub/providers/gemini"
	"done-hub/types"
	"testing"
)

func TestConvertClaudeToGemini(t *testing.T) {
	// 创建一个模拟的 ChatCompletionRequest
	request := &types.ChatCompletionRequest{
		Model: "gemini-1.5-pro",
		Messages: []types.ChatCompletionMessage{
			{
				Role:    types.ChatMessageRoleSystem,
				Content: "You are a helpful assistant.",
			},
			{
				Role:    types.ChatMessageRoleUser,
				Content: "Hello, how are you?",
			},
		},
		MaxTokens:   1000,
		Temperature: &[]float64{0.7}[0],
		Stream:      false,
	}

	// 调用转换函数
	result, err := ConvertClaudeToGemini(request)
	if err != nil {
		t.Fatalf("ConvertClaudeToGemini failed: %v", err)
	}

	// 检查结果类型
	geminiReq, ok := result.(*gemini.GeminiChatRequest)
	if !ok {
		t.Fatalf("Expected *gemini.GeminiChatRequest, got %T", result)
	}

	// 验证基本字段
	if geminiReq.Model != "gemini-1.5-pro" {
		t.<PERSON><PERSON><PERSON>("Expected model 'gemini-1.5-pro', got '%s'", geminiReq.Model)
	}

	if geminiReq.Stream != false {
		t.Errorf("Expected stream false, got %v", geminiReq.Stream)
	}

	// 验证系统指令
	if geminiReq.SystemInstruction == nil {
		t.Error("Expected SystemInstruction to be set")
	} else {
		sysInstr, ok := geminiReq.SystemInstruction.(*gemini.GeminiChatContent)
		if !ok {
			t.Error("Expected SystemInstruction to be *gemini.GeminiChatContent")
		} else {
			if len(sysInstr.Parts) == 0 {
				t.Error("Expected SystemInstruction to have parts")
			} else if sysInstr.Parts[0].Text != "You are a helpful assistant." {
				t.Errorf("Expected system instruction 'You are a helpful assistant.', got '%s'",
					sysInstr.Parts[0].Text)
			}
		}
	}

	// 验证内容
	if len(geminiReq.Contents) != 1 {
		t.Errorf("Expected 1 content, got %d", len(geminiReq.Contents))
	} else {
		content := geminiReq.Contents[0]
		if content.Role != "user" {
			t.Errorf("Expected role 'user', got '%s'", content.Role)
		}
		if len(content.Parts) != 1 {
			t.Errorf("Expected 1 part, got %d", len(content.Parts))
		} else if content.Parts[0].Text != "Hello, how are you?" {
			t.Errorf("Expected text 'Hello, how are you?', got '%s'", content.Parts[0].Text)
		}
	}

	// 验证生成配置
	if geminiReq.GenerationConfig.MaxOutputTokens != 1000 {
		t.Errorf("Expected MaxOutputTokens 1000, got %d", geminiReq.GenerationConfig.MaxOutputTokens)
	}
	if geminiReq.GenerationConfig.Temperature == nil || *geminiReq.GenerationConfig.Temperature != 0.7 {
		t.Errorf("Expected Temperature 0.7, got %v", geminiReq.GenerationConfig.Temperature)
	}
}

func TestConvertGeminiResponseToClaude(t *testing.T) {
	// 创建一个模拟的 Gemini 响应
	geminiResp := &gemini.GeminiChatResponse{
		ResponseId: "test-response-id",
		Candidates: []gemini.GeminiChatCandidate{
			{
				Content: gemini.GeminiChatContent{
					Parts: []gemini.GeminiPart{
						{
							Text: "Hello! I'm doing well, thank you for asking.",
						},
					},
				},
				FinishReason: &[]string{"STOP"}[0],
			},
		},
		UsageMetadata: &gemini.GeminiUsageMetadata{
			PromptTokenCount:     10,
			CandidatesTokenCount: 15,
			TotalTokenCount:      25,
		},
	}

	// 调用转换函数
	claudeResp := convertGeminiResponseToClaude(geminiResp, "gemini-1.5-pro")

	// 验证基本字段
	if claudeResp.Id != "test-response-id" {
		t.Errorf("Expected ID 'test-response-id', got '%s'", claudeResp.Id)
	}
	if claudeResp.Type != "message" {
		t.Errorf("Expected Type 'message', got '%s'", claudeResp.Type)
	}
	if claudeResp.Role != "assistant" {
		t.Errorf("Expected Role 'assistant', got '%s'", claudeResp.Role)
	}
	if claudeResp.Model != "gemini-1.5-pro" {
		t.Errorf("Expected Model 'gemini-1.5-pro', got '%s'", claudeResp.Model)
	}

	// 验证内容
	if len(claudeResp.Content) != 1 {
		t.Errorf("Expected 1 content, got %d", len(claudeResp.Content))
	} else {
		content := claudeResp.Content[0]
		if content.Type != "text" {
			t.Errorf("Expected content type 'text', got '%s'", content.Type)
		}
		if content.Text != "Hello! I'm doing well, thank you for asking." {
			t.Errorf("Expected text 'Hello! I'm doing well, thank you for asking.', got '%s'", content.Text)
		}
	}

	// 验证使用统计
	if claudeResp.Usage.InputTokens != 10 {
		t.Errorf("Expected InputTokens 10, got %d", claudeResp.Usage.InputTokens)
	}
	if claudeResp.Usage.OutputTokens != 15 {
		t.Errorf("Expected OutputTokens 15, got %d", claudeResp.Usage.OutputTokens)
	}

	// 验证停止原因
	if claudeResp.StopReason != "end_turn" {
		t.Errorf("Expected StopReason 'end_turn', got '%s'", claudeResp.StopReason)
	}
}

func TestConvertClaudeToGeminiWithTools(t *testing.T) {
	// 创建一个包含工具的请求
	request := &types.ChatCompletionRequest{
		Model: "gemini-1.5-pro",
		Messages: []types.ChatCompletionMessage{
			{
				Role:    types.ChatMessageRoleUser,
				Content: "What's the weather like?",
			},
		},
		Tools: []*types.ChatCompletionTool{
			{
				Type: "function",
				Function: types.ChatCompletionFunction{
					Name:        "get_weather",
					Description: "Get the current weather",
					Parameters: map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"location": map[string]interface{}{
								"type":        "string",
								"description": "The city and state",
							},
						},
						"required": []string{"location"},
					},
				},
			},
		},
		MaxTokens: 1000,
		Stream:    false,
	}

	// 调用转换函数
	result, err := ConvertClaudeToGemini(request)
	if err != nil {
		t.Fatalf("ConvertClaudeToGemini failed: %v", err)
	}

	// 检查结果类型
	geminiReq, ok := result.(*gemini.GeminiChatRequest)
	if !ok {
		t.Fatalf("Expected *gemini.GeminiChatRequest, got %T", result)
	}

	// 验证工具定义
	if len(geminiReq.Tools) != 1 {
		t.Errorf("Expected 1 tool, got %d", len(geminiReq.Tools))
	} else {
		tool := geminiReq.Tools[0]
		if len(tool.FunctionDeclarations) != 1 {
			t.Errorf("Expected 1 function declaration, got %d", len(tool.FunctionDeclarations))
		} else {
			funcDecl := tool.FunctionDeclarations[0]
			if funcDecl.Name != "get_weather" {
				t.Errorf("Expected function name 'get_weather', got '%s'", funcDecl.Name)
			}
			if funcDecl.Description != "Get the current weather" {
				t.Errorf("Expected function description 'Get the current weather', got '%s'", funcDecl.Description)
			}
		}
	}
}

func TestConvertGeminiResponseWithToolCall(t *testing.T) {
	// 创建一个包含工具调用的 Gemini 响应
	geminiResp := &gemini.GeminiChatResponse{
		ResponseId: "test-response-id",
		Candidates: []gemini.GeminiChatCandidate{
			{
				Content: gemini.GeminiChatContent{
					Parts: []gemini.GeminiPart{
						{
							FunctionCall: &gemini.GeminiFunctionCall{
								Name: "get_weather",
								Args: map[string]interface{}{
									"location": "San Francisco, CA",
								},
							},
						},
					},
				},
				FinishReason: &[]string{"STOP"}[0],
			},
		},
	}

	// 调用转换函数
	claudeResp := convertGeminiResponseToClaude(geminiResp, "gemini-1.5-pro")

	// 验证工具调用
	if len(claudeResp.Content) != 1 {
		t.Errorf("Expected 1 content, got %d", len(claudeResp.Content))
	} else {
		content := claudeResp.Content[0]
		if content.Type != "tool_use" {
			t.Errorf("Expected content type 'tool_use', got '%s'", content.Type)
		}
		if content.Name != "get_weather" {
			t.Errorf("Expected tool name 'get_weather', got '%s'", content.Name)
		}
		
		// 验证工具参数
		args, ok := content.Input.(map[string]interface{})
		if !ok {
			t.Error("Expected Input to be map[string]interface{}")
		} else {
			location, exists := args["location"]
			if !exists {
				t.Error("Expected 'location' parameter in tool call")
			} else if location != "San Francisco, CA" {
				t.Errorf("Expected location 'San Francisco, CA', got '%v'", location)
			}
		}
	}
}
