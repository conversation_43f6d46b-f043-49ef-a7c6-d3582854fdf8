package category

import (
	"done-hub/common"
	"done-hub/common/logger"
	"done-hub/common/requester"
	"done-hub/providers/base"
	"done-hub/providers/claude"
	"done-hub/providers/gemini"
	"done-hub/types"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

func init() {
	CategoryMap["gemini-claude"] = &Category{
		Category:                  "gemini-claude",
		ChatComplete:              ConvertClaudeToGemini,
		ResponseChatComplete:      ConvertGeminiToClaude,
		ResponseChatCompleteStrem: GeminiClaudeStreamHandler,
		ErrorHandler:              gemini.RequestErrorHandle(""),
		GetModelName:              GetGeminiModelName,
		GetOtherUrl:               getGeminiOtherUrl,
	}
}

// ConvertClaudeToGemini 将 Claude 格式请求转换为 Gemini 格式
func ConvertClaudeToGemini(request *types.ChatCompletionRequest) (any, *types.OpenAIErrorWithStatusCode) {
	logger.SysLog(fmt.Sprintf("ConvertClaudeToGemini: Converting request for model %s", request.Model))

	// 首先将 OpenAI 格式转换为 Claude 格式
	claudeRequest, err := claude.ConvertFromChatOpenai(request)
	if err != nil {
		logger.SysError(fmt.Sprintf("ConvertClaudeToGemini: Failed to convert from OpenAI to Claude: %v", err))
		return nil, err
	}

	logger.SysLog(fmt.Sprintf("ConvertClaudeToGemini: Successfully converted to Claude format, messages: %d", len(claudeRequest.Messages)))

	// 然后将 Claude 格式转换为 Gemini 格式
	geminiRequest, err := convertClaudeRequestToGemini(claudeRequest)
	if err != nil {
		logger.SysError(fmt.Sprintf("ConvertClaudeToGemini: Failed to convert from Claude to Gemini: %v", err))
		return nil, err
	}

	logger.SysLog("ConvertClaudeToGemini: Successfully converted to Gemini format")
	return geminiRequest, nil
}

// convertClaudeRequestToGemini 将 Claude 请求转换为 Gemini 请求
func convertClaudeRequestToGemini(claudeReq *claude.ClaudeRequest) (*gemini.GeminiChatRequest, *types.OpenAIErrorWithStatusCode) {
	logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Starting conversion for model %s", claudeReq.Model))

	geminiReq := &gemini.GeminiChatRequest{
		Model:  claudeReq.Model,
		Stream: claudeReq.Stream,
	}

	// 转换消息
	contents := make([]gemini.GeminiChatContent, 0)

	// 处理系统消息
	if claudeReq.System != nil {
		systemContent := ""
		switch sys := claudeReq.System.(type) {
		case string:
			systemContent = sys
		case []interface{}:
			for _, item := range sys {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if text, exists := itemMap["text"]; exists {
						if textStr, ok := text.(string); ok {
							systemContent += textStr + "\n"
						}
					}
				}
			}
		}

		if systemContent != "" {
			// 使用 SystemInstruction 而不是添加到 contents
			geminiReq.SystemInstruction = &gemini.GeminiChatContent{
				Parts: []gemini.GeminiPart{
					{Text: systemContent},
				},
			}
		}
	}

	// 转换对话消息
	logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Processing %d Claude messages", len(claudeReq.Messages)))

	for i, msg := range claudeReq.Messages {
		logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Processing message %d, role: %s", i, msg.Role))

		geminiContent := gemini.GeminiChatContent{}

		// 转换角色
		switch msg.Role {
		case "user":
			geminiContent.Role = "user"
		case "assistant":
			geminiContent.Role = "model"
		default:
			geminiContent.Role = "user"
		}

		// 转换内容
		parts := make([]gemini.GeminiPart, 0)

		switch content := msg.Content.(type) {
		case string:
			logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Message %d content is string: %s", i, content))
			if content != "" {
				parts = append(parts, gemini.GeminiPart{Text: content})
			}
		case []claude.MessageContent:
			logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Message %d content is array with %d items", i, len(content)))
			for j, item := range content {
				logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Processing content item %d, type: %s", j, item.Type))
				switch item.Type {
				case "text":
					if item.Text != "" {
						parts = append(parts, gemini.GeminiPart{Text: item.Text})
						logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Added text part: %s", item.Text))
					}
				case "tool_use":
					// 转换工具调用
					args := make(map[string]interface{})
					if item.Input != nil {
						args = item.Input.(map[string]interface{})
					}
					parts = append(parts, gemini.GeminiPart{
						FunctionCall: &gemini.GeminiFunctionCall{
							Name: item.Name,
							Args: args,
						},
					})
					logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Added tool call: %s", item.Name))
				case "tool_result":
					// 工具结果转换为文本
					resultText := fmt.Sprintf("Tool result for %s: %v", item.ToolUseId, item.Content)
					parts = append(parts, gemini.GeminiPart{Text: resultText})
					logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Added tool result: %s", resultText))
				}
			}
		default:
			logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Message %d content type unknown: %T", i, content))
		}

		logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Message %d generated %d parts", i, len(parts)))

		if len(parts) > 0 {
			geminiContent.Parts = parts
			contents = append(contents, geminiContent)
			logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Added content for message %d", i))
		} else {
			logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Skipped message %d (no parts)", i))
		}
	}

	geminiReq.Contents = contents

	// 转换工具定义
	if len(claudeReq.Tools) > 0 {
		tools := make([]gemini.GeminiChatTools, 0)
		functionDeclarations := make([]types.ChatCompletionFunction, 0)

		for _, tool := range claudeReq.Tools {
			functionDeclarations = append(functionDeclarations, types.ChatCompletionFunction{
				Name:        tool.Name,
				Description: tool.Description,
				Parameters:  tool.InputSchema,
			})
		}

		if len(functionDeclarations) > 0 {
			tools = append(tools, gemini.GeminiChatTools{
				FunctionDeclarations: functionDeclarations,
			})
			geminiReq.Tools = tools
		}
	}

	// 转换生成配置
	genConfig := gemini.GeminiChatGenerationConfig{}

	if claudeReq.MaxTokens > 0 {
		genConfig.MaxOutputTokens = claudeReq.MaxTokens
	}
	if claudeReq.Temperature != nil {
		genConfig.Temperature = claudeReq.Temperature
	}
	if claudeReq.TopP != nil {
		genConfig.TopP = claudeReq.TopP
	}
	if claudeReq.TopK != nil {
		topKFloat := float64(*claudeReq.TopK)
		genConfig.TopK = &topKFloat
	}
	if len(claudeReq.StopSequences) > 0 {
		genConfig.StopSequences = claudeReq.StopSequences
	}

	geminiReq.GenerationConfig = genConfig

	logger.SysLog(fmt.Sprintf("convertClaudeRequestToGemini: Conversion completed. Contents: %d, Tools: %d",
		len(geminiReq.Contents), len(geminiReq.Tools)))

	return geminiReq, nil
}

// ConvertGeminiToClaude 将 Gemini 响应转换为 Claude 格式
func ConvertGeminiToClaude(provider base.ProviderInterface, response *http.Response, request *types.ChatCompletionRequest) (*types.ChatCompletionResponse, *types.OpenAIErrorWithStatusCode) {
	logger.SysLog("ConvertGeminiToClaude: Starting response conversion")

	geminiResponse := &gemini.GeminiChatResponse{}
	err := json.NewDecoder(response.Body).Decode(geminiResponse)
	if err != nil {
		logger.SysError(fmt.Sprintf("ConvertGeminiToClaude: Failed to decode Gemini response: %v", err))
		return nil, common.ErrorWrapper(err, "decode_response_failed", http.StatusInternalServerError)
	}

	logger.SysLog(fmt.Sprintf("ConvertGeminiToClaude: Decoded Gemini response with %d candidates", len(geminiResponse.Candidates)))

	// 将 Gemini 响应直接转换为 Claude 格式，不再转换为 OpenAI 格式
	claudeResponse := ConvertGeminiResponseToClaude(geminiResponse, request.Model)

	logger.SysLog(fmt.Sprintf("ConvertGeminiToClaude: Converted to Claude format with %d content items", len(claudeResponse.Content)))

	// 直接返回 Claude 格式的响应，模拟 OpenAI 响应结构但内容是 Claude 格式
	openaiResponse := &types.ChatCompletionResponse{
		ID:      claudeResponse.Id,
		Object:  "chat.completion",
		Created: int64(len(claudeResponse.Id)), // 简单的时间戳
		Model:   claudeResponse.Model,
		Choices: []types.ChatCompletionChoice{
			{
				Index: 0,
				Message: types.ChatCompletionMessage{
					Role:    claudeResponse.Role,
					Content: claudeResponse, // 直接将 Claude 响应作为内容
				},
				FinishReason: convertClaudeStopReasonToOpenAI(claudeResponse.StopReason),
			},
		},
		Usage: &types.Usage{
			PromptTokens:     claudeResponse.Usage.InputTokens,
			CompletionTokens: claudeResponse.Usage.OutputTokens,
			TotalTokens:      claudeResponse.Usage.InputTokens + claudeResponse.Usage.OutputTokens,
		},
	}

	logger.SysLog("ConvertGeminiToClaude: Successfully converted to Claude format")
	return openaiResponse, nil
}

// convertClaudeStopReasonToOpenAI 转换停止原因
func convertClaudeStopReasonToOpenAI(stopReason string) string {
	switch stopReason {
	case "end_turn":
		return "stop"
	case "max_tokens":
		return "length"
	case "tool_use":
		return "tool_calls"
	case "stop_sequence":
		return "content_filter"
	default:
		return "stop"
	}
}

// ConvertGeminiResponseToClaude 将 Gemini 响应转换为 Claude 响应
func ConvertGeminiResponseToClaude(geminiResp *gemini.GeminiChatResponse, model string) *claude.ClaudeResponse {
	claudeResp := &claude.ClaudeResponse{
		Id:    geminiResp.ResponseId,
		Type:  "message",
		Role:  "assistant",
		Model: model,
	}

	if len(geminiResp.Candidates) == 0 {
		claudeResp.Content = []claude.ResContent{}
		return claudeResp
	}

	candidate := geminiResp.Candidates[0]
	content := make([]claude.ResContent, 0)

	// 转换内容部分
	for _, part := range candidate.Content.Parts {
		if part.Text != "" {
			content = append(content, claude.ResContent{
				Type: "text",
				Text: part.Text,
			})
		}
		
		if part.FunctionCall != nil {
			content = append(content, claude.ResContent{
				Type:  "tool_use",
				Id:    fmt.Sprintf("tool_%s", part.FunctionCall.Name),
				Name:  part.FunctionCall.Name,
				Input: part.FunctionCall.Args,
			})
		}
	}

	claudeResp.Content = content

	// 转换使用统计
	if geminiResp.UsageMetadata != nil {
		claudeResp.Usage = claude.Usage{
			InputTokens:  geminiResp.UsageMetadata.PromptTokenCount,
			OutputTokens: geminiResp.UsageMetadata.CandidatesTokenCount,
		}
	}

	// 转换停止原因
	if candidate.FinishReason != nil {
		switch *candidate.FinishReason {
		case "STOP":
			claudeResp.StopReason = "end_turn"
		case "MAX_TOKENS":
			claudeResp.StopReason = "max_tokens"
		case "SAFETY":
			claudeResp.StopReason = "stop_sequence"
		default:
			claudeResp.StopReason = "end_turn"
		}
	} else {
		claudeResp.StopReason = "end_turn"
	}

	return claudeResp
}

// GeminiClaudeStreamHandler 处理流式响应
func GeminiClaudeStreamHandler(provider base.ProviderInterface, request *types.ChatCompletionRequest) requester.HandlerPrefix[string] {
	chatHandler := &GeminiClaudeStreamProcessor{
		Usage:   provider.GetUsage(),
		Request: request,
		Prefix:  `data: `,
	}

	return chatHandler.HandlerStream
}

// GeminiClaudeStreamProcessor 流式响应处理器
type GeminiClaudeStreamProcessor struct {
	Usage   *types.Usage
	Request *types.ChatCompletionRequest
	Prefix  string
}

// HandlerStream 处理流式数据
func (h *GeminiClaudeStreamProcessor) HandlerStream(rawLine *[]byte, dataChan chan string, errChan chan error) {
	// 如果rawLine 前缀不为data:，则直接返回
	if !strings.HasPrefix(string(*rawLine), h.Prefix) {
		*rawLine = nil
		return
	}

	if strings.HasPrefix(string(*rawLine), "data: ") {
		// 去除前缀
		*rawLine = (*rawLine)[6:]
	}

	var geminiResponse gemini.GeminiChatResponse
	err := json.Unmarshal(*rawLine, &geminiResponse)
	if err != nil {
		errChan <- common.ErrorToOpenAIError(err)
		return
	}

	// 检查是否结束
	if len(geminiResponse.Candidates) > 0 && geminiResponse.Candidates[0].FinishReason != nil &&
		(*geminiResponse.Candidates[0].FinishReason == "STOP" ||
		 *geminiResponse.Candidates[0].FinishReason == "MAX_TOKENS") {
		errChan <- io.EOF
		*rawLine = requester.StreamClosed
		return
	}

	// 转换为 Claude 格式
	claudeResponse := ConvertGeminiResponseToClaude(&geminiResponse, h.Request.Model)
	
	// 转换为 Claude 流式响应格式
	claudeStreamResp := convertToClaudeStreamResponse(claudeResponse)
	
	// 序列化并发送
	responseBytes, err := json.Marshal(claudeStreamResp)
	if err != nil {
		errChan <- common.ErrorToOpenAIError(err)
		return
	}

	dataChan <- string(responseBytes)
}

// convertToClaudeStreamResponse 转换为 Claude 流式响应格式
func convertToClaudeStreamResponse(claudeResp *claude.ClaudeResponse) claude.ClaudeStreamResponse {
	streamResp := claude.ClaudeStreamResponse{
		Type: "content_block_delta",
	}

	if len(claudeResp.Content) > 0 {
		content := claudeResp.Content[0]
		if content.Type == "text" {
			streamResp.Delta = claude.Delta{
				Type: "text_delta",
				Text: content.Text,
			}
		}
	}

	return streamResp
}
