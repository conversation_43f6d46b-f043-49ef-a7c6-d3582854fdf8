# Vertex AI Gemini-Claude 集成

## 概述

本实现允许客户端通过 Claude 原生的 `/claude/v1/messages` 接口来调用 Vertex AI 上的 Gemini 模型。这样用户可以使用熟悉的 Claude API 格式来与 Gemini 模型进行交互。

## 功能特性

### 支持的功能
- ✅ 基本文本对话
- ✅ 系统消息处理
- ✅ 工具调用 (Function Calling)
- ✅ 流式响应
- ✅ 非流式响应
- ✅ 温度、TopP、TopK 等参数
- ✅ 最大输出令牌数控制
- ✅ 停止序列
- ✅ 使用统计信息

### 转换逻辑

#### 请求转换 (Claude → Gemini)
1. **消息格式转换**：
   - Claude 的 `messages` → Gemini 的 `contents`
   - Claude 的 `system` → Gemini 的 `systemInstruction`
   - 角色映射：`user` → `user`, `assistant` → `model`

2. **工具定义转换**：
   - Claude 的 `tools` → Gemini 的 `tools.functionDeclarations`
   - 参数结构保持兼容

3. **生成配置转换**：
   - `max_tokens` → `maxOutputTokens`
   - `temperature` → `temperature`
   - `top_p` → `topP`
   - `top_k` → `topK`
   - `stop_sequences` → `stopSequences`

#### 响应转换 (Gemini → Claude)
1. **基本响应转换**：
   - Gemini 的 `candidates[0].content.parts` → Claude 的 `content`
   - 文本内容直接映射
   - 工具调用转换为 Claude 格式

2. **使用统计转换**：
   - `promptTokenCount` → `input_tokens`
   - `candidatesTokenCount` → `output_tokens`

3. **停止原因映射**：
   - `STOP` → `end_turn`
   - `MAX_TOKENS` → `max_tokens`
   - `SAFETY` → `stop_sequence`

## 使用方法

### 配置 Vertex AI 渠道
确保你的 Vertex AI 渠道已正确配置，包括：
- 项目 ID
- 区域设置
- 服务账户密钥

### 调用示例

#### 基本对话
```bash
curl -X POST http://your-server/claude/v1/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 1000,
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ]
  }'
```

#### 带系统消息的对话
```bash
curl -X POST http://your-server/claude/v1/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 1000,
    "system": "You are a helpful assistant.",
    "messages": [
      {
        "role": "user",
        "content": "What is the capital of France?"
      }
    ]
  }'
```

#### 工具调用
```bash
curl -X POST http://your-server/claude/v1/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 1000,
    "messages": [
      {
        "role": "user",
        "content": "What is the weather like in San Francisco?"
      }
    ],
    "tools": [
      {
        "name": "get_weather",
        "description": "Get the current weather",
        "input_schema": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state"
            }
          },
          "required": ["location"]
        }
      }
    ]
  }'
```

#### 流式响应
```bash
curl -X POST http://your-server/claude/v1/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 1000,
    "stream": true,
    "messages": [
      {
        "role": "user",
        "content": "Tell me a story"
      }
    ]
  }'
```

## 支持的模型

所有以 `gemini` 开头的模型都会通过此转换器处理，包括但不限于：
- `gemini-1.5-pro`
- `gemini-1.5-flash`
- `gemini-2.0-flash-exp`
- `gemini-1.0-pro`

## 技术实现

### 核心文件
- `providers/vertexai/category/gemini_claude.go` - 主要转换逻辑
- `providers/vertexai/category/base.go` - 类别识别逻辑
- `providers/vertexai/relay_claude.go` - Claude 接口处理

### 关键函数
- `ConvertClaudeToGemini()` - 将 Claude 请求转换为 Gemini 格式
- `ConvertGeminiToClaude()` - 将 Gemini 响应转换为 Claude 格式
- `GeminiClaudeStreamHandler()` - 处理流式响应

### 测试
运行测试以验证功能：
```bash
go test ./providers/vertexai/category -v
```

## 注意事项

1. **模型兼容性**：确保使用的 Gemini 模型在 Vertex AI 中可用
2. **权限配置**：确保 Vertex AI 服务账户有足够的权限
3. **区域设置**：某些模型可能只在特定区域可用
4. **令牌限制**：注意不同模型的令牌限制
5. **费用计算**：使用统计信息会正确反映实际的令牌使用量

## 错误处理

转换器会处理以下错误情况：
- 无效的请求格式
- 模型不支持
- Vertex AI API 错误
- 网络连接问题

错误会以 Claude API 兼容的格式返回。
