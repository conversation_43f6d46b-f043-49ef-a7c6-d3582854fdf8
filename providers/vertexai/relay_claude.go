package vertexai

import (
	"done-hub/common"
	"done-hub/common/logger"
	"done-hub/common/requester"
	"done-hub/providers/claude"
	"done-hub/providers/gemini"
	"done-hub/providers/vertexai/category"
	"done-hub/types"
	"encoding/json"
	"fmt"
	"net/http"
)

func (p *VertexAIProvider) CreateClaudeChat(request *claude.ClaudeRequest) (*claude.ClaudeResponse, *types.OpenAIErrorWithStatusCode) {
	req, errWithCode := p.getClaudeRequest(request)
	if errWithCode != nil {
		return nil, errWithCode
	}
	defer req.Body.Close()

	// 根据类别选择不同的响应处理方式
	if p.Category.Category == "gemini-claude" {
		logger.SysLog("CreateClaudeChat: Processing Gemini model response")

		// 对于 Gemini 模型，需要特殊处理响应
		resp, openaiErr := p.Requester.SendRequestRaw(req)
		if openaiErr != nil {
			return nil, openaiErr
		}
		defer resp.Body.Close()

		// 解码 Gemini 响应
		geminiResponse := &gemini.GeminiChatResponse{}
		err := json.NewDecoder(resp.Body).Decode(geminiResponse)
		if err != nil {
			logger.SysError(fmt.Sprintf("CreateClaudeChat: Failed to decode Gemini response: %v", err))
			return nil, common.ErrorWrapper(err, "decode_response_failed", http.StatusInternalServerError)
		}

		logger.SysLog(fmt.Sprintf("CreateClaudeChat: Decoded Gemini response with %d candidates", len(geminiResponse.Candidates)))

		// 转换为 Claude 格式
		claudeResponse := category.ConvertGeminiResponseToClaude(geminiResponse, request.Model)

		// 设置使用统计
		claude.ClaudeUsageToOpenaiUsage(&claudeResponse.Usage, p.GetUsage())

		logger.SysLog("CreateClaudeChat: Successfully converted Gemini response to Claude format")
		return claudeResponse, nil
	} else {
		logger.SysLog("CreateClaudeChat: Processing Claude model response")

		// 对于 Claude 模型，使用原有的处理方式
		claudeResponse := &claude.ClaudeResponse{}
		_, openaiErr := p.Requester.SendRequest(req, claudeResponse, false)
		if openaiErr != nil {
			return nil, openaiErr
		}

		claude.ClaudeUsageToOpenaiUsage(&claudeResponse.Usage, p.GetUsage())

		return claudeResponse, nil
	}
}

func (p *VertexAIProvider) CreateClaudeChatStream(request *claude.ClaudeRequest) (requester.StreamReaderInterface[string], *types.OpenAIErrorWithStatusCode) {
	req, errWithCode := p.getClaudeRequest(request)
	if errWithCode != nil {
		return nil, errWithCode
	}
	defer req.Body.Close()

	chatHandler := &claude.ClaudeRelayStreamHandler{
		Usage:     p.Usage,
		ModelName: request.Model,
		Prefix:    `data: {"type"`,
	}

	// 发送请求
	resp, openaiErr := p.Requester.SendRequestRaw(req)
	if openaiErr != nil {
		return nil, openaiErr
	}

	stream, openaiErr := requester.RequestNoTrimStream(p.Requester, resp, chatHandler.HandlerStream)
	if openaiErr != nil {
		return nil, openaiErr
	}

	return stream, nil
}

func (p *VertexAIProvider) getClaudeRequest(request *claude.ClaudeRequest) (*http.Request, *types.OpenAIErrorWithStatusCode) {
	var err error
	// 使用专门的 Claude API 类别判断函数
	p.Category, err = category.GetCategoryForClaudeAPI(request.Model)
	if err != nil {
		return nil, common.StringErrorWrapperLocal("vertexAI provider not found", "vertexAI_err", http.StatusInternalServerError)
	}

	// 支持 claude 和 gemini-claude 两种类别
	if p.Category.Category != "claude" && p.Category.Category != "gemini-claude" {
		return nil, common.StringErrorWrapperLocal("vertexAI provider not found", "vertexAI_err", http.StatusInternalServerError)
	}

	otherUrl := p.Category.GetOtherUrl(request.Stream)
	modelName := p.Category.GetModelName(request.Model)

	// 获取请求地址
	fullRequestURL := p.GetFullRequestURL(modelName, otherUrl)
	if fullRequestURL == "" {
		return nil, common.StringErrorWrapperLocal("vertexAI config error", "invalid_vertexai_config", http.StatusInternalServerError)
	}

	headers := p.GetRequestHeaders()

	if headers == nil {
		return nil, common.StringErrorWrapperLocal("vertexAI config error", "invalid_vertexai_config", http.StatusInternalServerError)
	}

	if request.Stream {
		headers["Accept"] = "text/event-stream"
	}

	copyRequest := *request

	var requestBody interface{}

	// 根据类别选择不同的请求体格式
	if p.Category.Category == "gemini-claude" {
		logger.SysLog("Processing Gemini model through Claude API: " + request.Model)

		// 对于 Gemini 模型，需要转换为 Gemini 格式
		// 首先创建一个临时的 ChatCompletionRequest 来进行转换
		tempChatRequest := &types.ChatCompletionRequest{
			Model:       request.Model,
			Messages:    convertClaudeMessagesToOpenAI(request),
			MaxTokens:   request.MaxTokens,
			Temperature: request.Temperature,
			TopP:        request.TopP,
			Stream:      request.Stream,
		}

		logger.SysLog("Converted Claude messages to OpenAI format, message count: " +
			fmt.Sprintf("%d", len(tempChatRequest.Messages)))

		// 使用 gemini-claude 类别的转换函数
		geminiRequest, errWithCode := p.Category.ChatComplete(tempChatRequest)
		if errWithCode != nil {
			logger.SysError("Failed to convert to Gemini format: " + errWithCode.Error())
			return nil, errWithCode
		}

		logger.SysLog("Successfully converted to Gemini format")

		// 对于 gemini-claude 转换，直接使用转换后的请求体，不需要清理 JSON
		// 因为这是我们自己生成的结构化数据，不是从原始 JSON 解析的
		requestBody = geminiRequest
	} else {
		logger.SysLog("Processing Claude model: " + request.Model)

		// 对于 Claude 模型，使用原有的格式
		vertexaiRequest := &category.ClaudeRequest{
			ClaudeRequest:    &copyRequest,
			AnthropicVersion: category.AnthropicVersion,
		}
		vertexaiRequest.Model = ""
		requestBody = vertexaiRequest
	}

	// 错误处理
	p.Requester.ErrorHandler = RequestErrorHandle(p.Category.ErrorHandler)

	// 使用BaseProvider的统一方法创建请求，支持额外参数处理
	req, errWithCode := p.NewRequestWithCustomParams(http.MethodPost, fullRequestURL, requestBody, headers, request.Model)
	if errWithCode != nil {
		return nil, errWithCode
	}
	return req, nil
}

// convertClaudeMessagesToOpenAI 将 Claude 消息格式转换为 OpenAI 格式
func convertClaudeMessagesToOpenAI(claudeReq *claude.ClaudeRequest) []types.ChatCompletionMessage {
	logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Converting %d Claude messages", len(claudeReq.Messages)))
	messages := make([]types.ChatCompletionMessage, 0)

	// 添加系统消息
	if claudeReq.System != nil {
		systemContent := ""
		switch sys := claudeReq.System.(type) {
		case string:
			systemContent = sys
		case []interface{}:
			for _, item := range sys {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if text, exists := itemMap["text"]; exists {
						if textStr, ok := text.(string); ok {
							systemContent += textStr + "\n"
						}
					}
				}
			}
		}

		if systemContent != "" {
			messages = append(messages, types.ChatCompletionMessage{
				Role:    types.ChatMessageRoleSystem,
				Content: systemContent,
			})
		}
	}

	// 转换对话消息
	for i, msg := range claudeReq.Messages {
		openaiMsg := types.ChatCompletionMessage{
			Role: convertClaudeRoleToOpenAI(msg.Role),
		}

		// 转换内容
		logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Message %d content type: %T", i, msg.Content))

		switch content := msg.Content.(type) {
		case string:
			logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Message %d is string: %s", i, content))
			openaiMsg.Content = content
		case []claude.MessageContent:
			logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Message %d is MessageContent array with %d items", i, len(content)))
			if len(content) == 1 && content[0].Type == "text" {
				// 简单文本消息
				logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Single text content: %s", content[0].Text))
				openaiMsg.Content = content[0].Text
			} else {
				// 复杂内容，转换为 OpenAI 格式
				openaiContent := make([]types.ChatMessagePart, 0)
				toolCalls := make([]*types.ChatCompletionToolCalls, 0)

				for j, item := range content {
					logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Processing content item %d, type: %s", j, item.Type))
					switch item.Type {
					case "text":
						if item.Text != "" {
							openaiContent = append(openaiContent, types.ChatMessagePart{
								Type: types.ContentTypeText,
								Text: item.Text,
							})
							logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Added text part: %s", item.Text))
						}
					case "tool_use":
						// 转换工具调用
						args, _ := json.Marshal(item.Input)
						toolCalls = append(toolCalls, &types.ChatCompletionToolCalls{
							Id:   item.Id,
							Type: types.ChatMessageRoleFunction,
							Function: &types.ChatCompletionToolCallsFunction{
								Name:      item.Name,
								Arguments: string(args),
							},
						})
						logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Added tool call: %s", item.Name))
					case "tool_result":
						// 工具结果转换为文本内容
						resultText := ""
						if contentStr, ok := item.Content.(string); ok {
							resultText = contentStr
						} else {
							resultBytes, _ := json.Marshal(item.Content)
							resultText = string(resultBytes)
						}
						openaiContent = append(openaiContent, types.ChatMessagePart{
							Type: types.ContentTypeText,
							Text: resultText,
						})
						logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Added tool result: %s", resultText))
					}
				}

				if len(toolCalls) > 0 {
					openaiMsg.ToolCalls = toolCalls
					logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Added %d tool calls", len(toolCalls)))
				}
				if len(openaiContent) > 0 {
					openaiMsg.Content = openaiContent
					logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Added %d content parts", len(openaiContent)))
				}
			}
		case []interface{}:
			// 处理原始的 interface{} 数组（来自 Claude 客户端）
			logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Message %d is interface array with %d items", i, len(content)))
			textParts := make([]string, 0)

			for j, item := range content {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if itemType, exists := itemMap["type"]; exists && itemType == "text" {
						if text, textExists := itemMap["text"]; textExists {
							if textStr, ok := text.(string); ok {
								textParts = append(textParts, textStr)
								logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Extracted text from item %d: %s", j, textStr))
							}
						}
					}
				}
			}

			if len(textParts) > 0 {
				// 合并所有文本部分
				combinedText := ""
				for _, part := range textParts {
					if combinedText != "" {
						combinedText += "\n"
					}
					combinedText += part
				}
				openaiMsg.Content = combinedText
				logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Combined text content: %s", combinedText))
			} else {
				openaiMsg.Content = fmt.Sprintf("%v", content)
			}
		default:
			logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Unknown content type %T, converting to string", content))
			openaiMsg.Content = fmt.Sprintf("%v", content)
		}

		messages = append(messages, openaiMsg)
	}

	logger.SysLog(fmt.Sprintf("convertClaudeMessagesToOpenAI: Converted to %d OpenAI messages", len(messages)))
	return messages
}

// convertClaudeRoleToOpenAI 转换角色
func convertClaudeRoleToOpenAI(role string) string {
	switch role {
	case "user":
		return types.ChatMessageRoleUser
	case "assistant":
		return types.ChatMessageRoleAssistant
	case "system":
		return types.ChatMessageRoleSystem
	default:
		return types.ChatMessageRoleUser
	}
}
