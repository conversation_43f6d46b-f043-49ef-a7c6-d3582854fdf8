#!/bin/bash

# 简单的测试脚本，模拟 Claude 客户端的请求格式

SERVER_URL="http://localhost:3000"
API_KEY="your-api-key"

echo "Testing simple Gemini request through Claude API..."

curl -X POST "${SERVER_URL}/claude/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_KEY}" \
  -d '{
    "model": "gemini-2.5-pro",
    "max_tokens": 100,
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "Hello, how are you?"
          }
        ]
      }
    ]
  }' \
  -v

echo -e "\nTest completed!"
