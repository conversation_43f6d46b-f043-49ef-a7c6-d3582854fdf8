package main

import (
	"done-hub/providers/vertexai/category"
	"done-hub/types"
	"encoding/json"
	"fmt"
	"log"
)

func main() {
	fmt.Println("Testing Gemini-Claude conversion...")

	// 创建一个测试请求
	request := &types.ChatCompletionRequest{
		Model: "gemini-1.5-pro",
		Messages: []types.ChatCompletionMessage{
			{
				Role:    types.ChatMessageRoleSystem,
				Content: "You are a helpful assistant.",
			},
			{
				Role:    types.ChatMessageRoleUser,
				Content: "Hello, how are you?",
			},
		},
		MaxTokens:   1000,
		Temperature: &[]float64{0.7}[0],
		Stream:      false,
	}

	fmt.Printf("Original request: %+v\n", request)

	// 测试转换
	result, err := category.ConvertClaudeToGemini(request)
	if err != nil {
		log.Fatalf("Conversion failed: %v", err)
	}

	// 打印结果
	resultJSON, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("Converted result:\n%s\n", string(resultJSON))

	fmt.Println("Conversion test completed successfully!")
}
