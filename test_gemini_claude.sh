#!/bin/bash

# 测试 Gemini 模型通过 Claude API 调用的脚本

# 设置变量
SERVER_URL="http://localhost:3000"  # 根据你的服务器地址调整
API_KEY="your-api-key"              # 替换为你的 API 密钥

echo "Testing Gemini model through Claude API..."

# 测试基本对话
echo "1. Testing basic conversation..."
curl -X POST "${SERVER_URL}/claude/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_KEY}" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 100,
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ]
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n" 

# 测试带系统消息的对话
echo "2. Testing conversation with system message..."
curl -X POST "${SERVER_URL}/claude/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_KEY}" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 100,
    "system": "You are a helpful assistant.",
    "messages": [
      {
        "role": "user",
        "content": "What is 2+2?"
      }
    ]
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n"

# 测试流式响应
echo "3. Testing streaming response..."
curl -X POST "${SERVER_URL}/claude/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_KEY}" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 50,
    "stream": true,
    "messages": [
      {
        "role": "user",
        "content": "Count from 1 to 5"
      }
    ]
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n"

# 测试工具调用
echo "4. Testing function calling..."
curl -X POST "${SERVER_URL}/claude/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_KEY}" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 100,
    "messages": [
      {
        "role": "user",
        "content": "What is the weather like in San Francisco?"
      }
    ],
    "tools": [
      {
        "name": "get_weather",
        "description": "Get the current weather",
        "input_schema": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state"
            }
          },
          "required": ["location"]
        }
      }
    ]
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\nTest completed!"
