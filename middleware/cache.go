package middleware

import (
	"strings"
	"github.com/gin-gonic/gin"
)

// StaticCache 只对静态资源设置缓存
func StaticCache() func(c *gin.Context) {
	return func(c *gin.Context) {
		uri := c.Request.RequestURI

		// 只对静态资源设置缓存
		if isStaticResource(uri) {
			c.<PERSON>("Cache-Control", "public, max-age=604800") // one week
		} else {
			c<PERSON>("Cache-Control", "no-cache")
		}
		c.Next()
	}
}

// NoCache 强制不缓存
func NoCache() func(c *gin.Context) {
	return func(c *gin.Context) {
		c.<PERSON>("Cache-Control", "no-cache, no-store, must-revalidate")
		c.<PERSON>("Pragma", "no-cache")
		c.<PERSON>("Expires", "0")
		c.Next()
	}
}

// Cache 兼容性保持，使用StaticCache
func Cache() func(c *gin.Context) {
	return StaticCache()
}

// 判断是否为静态资源
func isStaticResource(uri string) bool {
	staticExtensions := []string{
		".js", ".css", ".png", ".jpg", ".jpeg", ".gif", ".ico",
		".svg", ".woff", ".woff2", ".ttf", ".eot", ".map",
	}

	for _, ext := range staticExtensions {
		if strings.HasSuffix(uri, ext) {
			return true
		}
	}
	return false
}
